import { useEffect, useState, useCallback } from "react";
import { usePara<PERSON>, Link } from "react-router";
import { Loader2, ArrowLeft, Check } from "lucide-react";

import type { Route } from "./+types/[activityId]";
import { useAppContext } from "~/lib/providers/app-context";
import {
  useProfile,
  useMyCohortModule,
  useFeedEditPost,
} from "~/lib/api/client-queries";
import {
  FeedPostCard,
  type EnrichedActivityWithText,
} from "~/components/feed/FeedPostCard";
import { ImageViewer } from "~/components/feed/ImageViewer";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Post - Sphere` },
    { name: "description", content: "Individual post view" },
  ];
}

export default function PostDetailPage() {
  const { groupId, cohortId, moduleId, activityId } = useParams();
  const { userId, streamClient } = useAppContext();
  const { data: profileData } = useProfile();

  // Get module data to access feed configuration
  const {
    data: moduleResponse,
    isLoading: moduleLoading,
    isError: moduleError,
  } = useMyCohortModule(groupId!, cohortId!, moduleId!);

  // Edit post mutation
  const editPostMutation = useFeedEditPost(
    moduleResponse?.data?.config?.feedGroup || "",
    moduleResponse?.data?.config?.feedId || ""
  );

  const [activity, setActivity] = useState<EnrichedActivityWithText | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [shareDropdownOpen, setShareDropdownOpen] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [imageViewerOpen, setImageViewerOpen] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string>("");
  const [selectedImageAlt, setSelectedImageAlt] = useState<string>("");

  // Fetch individual activity
  useEffect(() => {
    const fetchActivity = async () => {
      if (!streamClient || !moduleResponse?.data || moduleLoading) return;

      try {
        setLoading(true);
        setError(null);

        const module = moduleResponse.data;

        // Get the feed using the config from the module
        const feed = streamClient.feed(
          module.config.feedGroup,
          module.config.feedId
        );

        // Fetch the specific activity
        const response = await feed.get({
          limit: 1,
          id_lte: activityId,
          id_gte: activityId,
          withReactionCounts: true,
          withOwnReactions: true,
          enrich: true,
        });

        if (response.results.length === 0) {
          setError("Post not found");
          return;
        }

        const activityData = response.results[0] as EnrichedActivityWithText;
        setActivity(activityData);
      } catch (err) {
        console.error("Error fetching activity:", err);
        setError("Failed to load post. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchActivity();
  }, [streamClient, moduleResponse, moduleLoading, activityId]);

  // Close share dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShareDropdownOpen(false);
    };

    if (shareDropdownOpen) {
      document.addEventListener("click", handleClickOutside);
      return () => {
        document.removeEventListener("click", handleClickOutside);
      };
    }
  }, [shareDropdownOpen]);

  const handleLike = async () => {
    if (!streamClient || !activity) return;

    try {
      const reaction = await streamClient.reactions.add("like", activity.id);

      setActivity({
        ...activity,
        own_reactions: {
          ...activity.own_reactions,
          like: [...(activity.own_reactions?.like || []), reaction],
        },
        reaction_counts: {
          ...activity.reaction_counts,
          like: (activity.reaction_counts?.like || 0) + 1,
        },
      });
    } catch (error) {
      console.error("Error adding like:", error);
    }
  };

  const handleUnlike = async (reactionId: string) => {
    if (!streamClient || !activity) return;

    try {
      await streamClient.reactions.delete(reactionId);

      setActivity({
        ...activity,
        own_reactions: {
          ...activity.own_reactions,
          like: activity.own_reactions?.like?.filter(
            (r) => r.id !== reactionId
          ),
        },
        reaction_counts: {
          ...activity.reaction_counts,
          like: Math.max((activity.reaction_counts?.like || 0) - 1, 0),
        },
      });
    } catch (error) {
      console.error("Error removing like:", error);
    }
  };

  const handleShare = async () => {
    const postUrl = window.location.href;

    try {
      await navigator.clipboard.writeText(postUrl);
      setShowToast(true);
      setTimeout(() => setShowToast(false), 3000);
      console.log("Link copied to clipboard!");
    } catch (err) {
      // Fallback for older browsers
      try {
        const textArea = document.createElement("textarea");
        textArea.value = postUrl;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        document.body.removeChild(textArea);
        setShowToast(true);
        setTimeout(() => setShowToast(false), 3000);
        console.log("Link copied to clipboard (fallback)!");
      } catch (fallbackErr) {
        console.error("Failed to copy link:", fallbackErr);
      }
    }
  };

  const handleImageClick = useCallback((imageUrl: string, alt: string) => {
    setSelectedImageUrl(imageUrl);
    setSelectedImageAlt(alt);
    setImageViewerOpen(true);
  }, []);

  const handleCloseImageViewer = useCallback(() => {
    setImageViewerOpen(false);
    setSelectedImageUrl("");
    setSelectedImageAlt("");
  }, []);

  const handleCommentFromCard = useCallback(
    async (activityId: string, text: string) => {
      if (!streamClient || !text.trim() || !activity) return;

      try {
        const comment = await streamClient.reactions.add(
          "comment",
          activityId,
          {
            text: text,
          }
        );

        // Update activity comment count
        setActivity({
          ...activity,
          reaction_counts: {
            ...activity.reaction_counts,
            comment: (activity.reaction_counts?.comment || 0) + 1,
          },
        });
      } catch (error) {
        console.error("Error adding comment:", error);
      }
    },
    [streamClient, activity]
  );

  const handleEdit = useCallback(
    async (
      activityId: string,
      text: string,
      attachment?: {
        type: string;
        url: string;
        fileName?: string;
      },
      ogData?: any
    ) => {
      if (!activity) {
        console.error("Cannot edit post: activity not found");
        throw new Error("Post not found");
      }

      if (!activity.foreign_id || !activity.time) {
        console.error(
          "Cannot edit post: missing foreign_id or time. This post was created before editing was supported."
        );
        throw new Error(
          "This post cannot be edited. Only posts created after the edit feature was added can be modified."
        );
      }

      try {
        await editPostMutation.mutateAsync({
          activityId,
          foreignId: activity.foreign_id,
          time: activity.time,
          text,
          attachment,
          ogData,
        });

        // Update local activity state
        const updatedActivity = {
          ...activity,
          message: text,
        };

        // Update attachments
        if (attachment) {
          if (attachment.type === "image") {
            updatedActivity.attachments = [
              {
                type: "image",
                image_url: attachment.url,
                custom: attachment.fileName
                  ? { fileName: attachment.fileName }
                  : {},
              },
            ];
          } else {
            updatedActivity.attachments = [
              {
                type: "file",
                asset_url: attachment.url,
                custom: attachment.fileName
                  ? { fileName: attachment.fileName }
                  : {},
              },
            ];
          }
        } else {
          // Remove attachments if no attachment provided
          updatedActivity.attachments = [];
          // Also remove legacy image field
          delete updatedActivity.image;
        }

        // Update Open Graph data
        if (ogData) {
          updatedActivity.og = ogData;
        } else {
          // Remove OG data if not provided
          delete updatedActivity.og;
        }

        setActivity(updatedActivity);
      } catch (error) {
        console.error("Error editing post:", error);
        throw error;
      }
    },
    [activity, editPostMutation]
  );

  if (moduleLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (moduleError || error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black">
        <div className="bg-red-900/50 border border-red-700 rounded-lg p-4 text-red-300 mb-4">
          {error || "Failed to load post"}
        </div>
        <Link
          to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Feed
        </Link>
      </div>
    );
  }

  if (!activity) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black">
        <p className="text-zinc-400 mb-4">Post not found</p>
        <Link
          to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
          className="text-blue-400 hover:text-blue-300 flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Feed
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-black">
      {/* Top Bar */}
      <div className="sticky top-0 z-50 bg-black/10 backdrop-blur-md border-b border-zinc-900">
        <div className="max-w-4xl mx-auto px-8 py-4">
          <div className="flex items-center gap-4">
            <Link
              to={`/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}`}
              className="text-zinc-400 hover:text-white flex items-center gap-2 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              Back to Feed
            </Link>
          </div>
        </div>
      </div>

      {/* Post Content */}
      <div className="flex-1 overflow-auto bg-black">
        <div className="max-w-4xl mx-auto px-8 py-12">
          <FeedPostCard
            activity={activity}
            onLike={(activityId, isLiked, reactionId) => {
              if (isLiked && reactionId) {
                handleUnlike(reactionId);
              } else {
                handleLike();
              }
            }}
            onComment={handleCommentFromCard}
            onShare={handleShare}
            onEdit={handleEdit}
            onImageClick={handleImageClick}
            currentUserId={userId}
            showComments={true}
            isClickable={false}
          />
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 bg-zinc-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center gap-2">
          <Check className="w-4 h-4" />
          Link copied to clipboard!
        </div>
      )}

      {/* Image Viewer Modal */}
      <ImageViewer
        imageUrl={selectedImageUrl}
        alt={selectedImageAlt}
        isOpen={imageViewerOpen}
        onClose={handleCloseImageViewer}
      />
    </div>
  );
}
